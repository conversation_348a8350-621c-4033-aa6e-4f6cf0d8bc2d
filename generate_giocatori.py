import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from xml.etree.ElementTree import Element, ElementTree, tostring, parse
from datetime import datetime
import copy
import os
import json

# Variabili globali
csv_filepath = None
df = None
selected_players = None
template_root = None
xml_mapping = None

# Carica il mapping XML
def load_xml_mapping():
    global xml_mapping
    try:
        with open('xml_mapping.json', 'r', encoding='utf-8') as f:
            xml_mapping = json.load(f)
        return True
    except Exception as e:
        messagebox.showerror("Errore", f"Impossibile caricare il file xml_mapping.json: {str(e)}")
        return False

# Carica il template XML
def load_template():
    global template_root
    try:
        template_root = parse('template.xml')
        return True
    except Exception as e:
        messagebox.showerror("Errore", f"Impossibile caricare il file template.xml: {str(e)}")
        return False

# Genera XML per i giocatori selezionati
def generate_players_xml(players_data):
    players_root = Element('list', id="db_changes")
    
    for index, player_data in players_data.iterrows():
        for record in template_root.findall('.//record'):
            new_record = copy.deepcopy(record)
            skip_record = False
            birth_date_handled = False
            
            for elem in new_record.iter():
                # Gestione speciale per le date di nascita
                if elem.tag == 'date' and elem.get('id') == 'new_value':
                    date_str = player_data.get('DATA DI NASCITA', '')
                    if date_str and pd.notna(date_str):
                        try:
                            date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                            day = date_obj.day
                            month = date_obj.month - 1  # Adjust month to be 0-based
                            year = date_obj.year
                            elem.set('day', str(day))
                            elem.set('month', str(month))
                            elem.set('year', str(year))
                            birth_date_handled = True
                        except Exception as e:
                            print(f"Errore nella conversione della data {date_str}: {str(e)}")
                
                # Gestione dei placeholder
                text = elem.text if elem.text is not None else ''
                value = elem.get('value', '')
                
                if '{{' in text or '{{' in value:
                    placeholder = text[2:-2] if '{{' in text else value[2:-2]
                    
                    if placeholder in player_data and pd.notna(player_data[placeholder]):
                        value = str(player_data[placeholder])
                        
                        # Skip the record if it matches certain conditions
                        if (placeholder == 'GOL IN NAZIONALE' and value == '0') or \
                           (placeholder == 'NUMERO DI PRESENZE IN NAZIONALE' and value == '0') or \
                           (placeholder in ['GK', 'DL', 'DR', 'DC', 'WBL', 'WBR', 'DM', 'ML', 'MR', 'MC', 'AMC', 'AML', 'AMR', 'ST'] and value == '1'):
                            skip_record = True
                            break
                        
                        if '{{' in text:
                            elem.text = value
                        else:
                            elem.set('value', value)
                    else:
                        # Se il placeholder non è presente nei dati, saltiamo questo record
                        skip_record = True
                        break
            
            if not skip_record or birth_date_handled:
                players_root.append(new_record)
    
    return players_root

# Carica il file CSV
def load_csv():
    global csv_filepath, df
    try:
        csv_filepath = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
        if not csv_filepath:
            return False
        
        df = pd.read_csv(csv_filepath, low_memory=False)
        update_club_dropdown()
        return True
    except Exception as e:
        messagebox.showerror("Errore", f"Impossibile caricare il file CSV: {str(e)}")
        return False

# Aggiorna il dropdown dei club
def update_club_dropdown():
    if df is not None and 'CLUB' in df.columns:
        clubs = df['CLUB'].dropna().unique().tolist()
        # Converti in stringhe
        clubs = [str(int(club)) if pd.notna(club) and not pd.isna(club) else "" for club in clubs]
        club_dropdown['values'] = clubs

# Mostra i giocatori del club selezionato
def show_players():
    global selected_players
    try:
        if not club_var.get():
            messagebox.showwarning("Attenzione", "Seleziona un club prima di mostrare i giocatori.")
            return
        
        club = int(club_var.get())
        selected_players = df[df['CLUB'] == club]
        
        player_list.delete(0, tk.END)
        for index, row in selected_players.iterrows():
            player_name = row.get('NOME COMPLETO', f"Giocatore {index}")
            player_list.insert(tk.END, player_name)
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nel mostrare i giocatori: {str(e)}")

# Mostra la finestra di modifica per il giocatore selezionato
def show_edit_window():
    try:
        if not player_list.curselection():
            messagebox.showwarning("Attenzione", "Seleziona un giocatore prima di modificarlo.")
            return
        
        selected_index = player_list.curselection()[0]
        selected_player = selected_players.iloc[selected_index]

        edit_window = tk.Toplevel(root)
        edit_window.title(f"Modifica dettagli per {selected_player.get('NOME COMUNE', 'Giocatore')}")
        edit_window.geometry("600x600")

        # Crea un canvas con scrollbar
        canvas = tk.Canvas(edit_window)
        scrollbar = tk.Scrollbar(edit_window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Aggiungi i campi di modifica
        entries = {}
        row = 0
        for column in selected_players.columns:
            tk.Label(scrollable_frame, text=column, anchor="w").grid(row=row, column=0, sticky="w", padx=5, pady=2)
            var = tk.StringVar(value=str(selected_player[column]) if pd.notna(selected_player[column]) else "")
            entry = tk.Entry(scrollable_frame, textvariable=var, width=50)
            entry.grid(row=row, column=1, sticky="ew", padx=5, pady=2)
            entries[column] = var
            row += 1

        # Pulsante per salvare le modifiche
        tk.Button(scrollable_frame, text="Salva", command=lambda: save_changes(selected_index, entries, edit_window)).grid(row=row, column=0, columnspan=2, pady=10)
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nell'apertura della finestra di modifica: {str(e)}")

# Salva le modifiche al giocatore
def save_changes(index, entries, window):
    try:
        for column, var in entries.items():
            value = var.get()
            # Converti i valori numerici
            if column in ['CLUB', 'ABILITÀ ATTUALE', 'ABILITÀ POTENZIALE', 'REPUTAZIONE ATTUALE', 'REPUTAZIONE NAZIONALE', 'REPUTAZIONE MONDIALE']:
                try:
                    value = int(value) if value else None
                except ValueError:
                    pass
            selected_players.at[selected_players.index[index], column] = value
        
        df.update(selected_players)
        if csv_filepath:
            df.to_csv(csv_filepath, index=False)
            messagebox.showinfo("Successo", "Modifiche salvate con successo!")
        window.destroy()
        
        # Aggiorna la lista dei giocatori
        show_players()
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nel salvare le modifiche: {str(e)}")

# Genera il file XML per i giocatori selezionati
def generate_xml():
    try:
        if not player_list.curselection():
            messagebox.showwarning("Attenzione", "Seleziona almeno un giocatore prima di generare l'XML.")
            return
        
        selected_indices = player_list.curselection()
        selected_players_data = selected_players.iloc[list(selected_indices)]
        
        if template_root is None and not load_template():
            return
        
        players_xml = generate_players_xml(selected_players_data)
        
        # Chiedi all'utente dove salvare il file
        output_filepath = filedialog.asksaveasfilename(
            defaultextension=".xml",
            filetypes=[("XML files", "*.xml")],
            initialfile="players_output.xml"
        )
        
        if not output_filepath:
            return
        
        # Salva il file XML
        players_xml_str = tostring(players_xml, encoding='utf-16').decode('utf-16')
        with open(output_filepath, 'w', encoding='utf-16') as f:
            f.write('<?xml version="1.0" encoding="UTF-16"?>\n')
            f.write(players_xml_str)
        
        messagebox.showinfo("Successo", f"File XML generato con successo: {output_filepath}")
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nella generazione dell'XML: {str(e)}")

# Crea un nuovo giocatore
def create_new_player():
    try:
        if df is None:
            messagebox.showwarning("Attenzione", "Carica prima un file CSV.")
            return
        
        # Crea una nuova finestra per inserire i dati del giocatore
        new_player_window = tk.Toplevel(root)
        new_player_window.title("Crea nuovo giocatore")
        new_player_window.geometry("600x600")
        
        # Crea un canvas con scrollbar
        canvas = tk.Canvas(new_player_window)
        scrollbar = tk.Scrollbar(new_player_window, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Aggiungi i campi per il nuovo giocatore
        entries = {}
        row = 0
        for column in df.columns:
            tk.Label(scrollable_frame, text=column, anchor="w").grid(row=row, column=0, sticky="w", padx=5, pady=2)
            var = tk.StringVar()
            entry = tk.Entry(scrollable_frame, textvariable=var, width=50)
            entry.grid(row=row, column=1, sticky="ew", padx=5, pady=2)
            entries[column] = var
            row += 1
        
        # Pulsante per salvare il nuovo giocatore
        tk.Button(scrollable_frame, text="Salva", command=lambda: save_new_player(entries, new_player_window)).grid(row=row, column=0, columnspan=2, pady=10)
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nella creazione del nuovo giocatore: {str(e)}")

# Salva il nuovo giocatore
def save_new_player(entries, window):
    try:
        global df, selected_players
        
        # Crea un nuovo dizionario con i dati del giocatore
        new_player_data = {}
        for column, var in entries.items():
            value = var.get()
            # Converti i valori numerici
            if column in ['CLUB', 'ABILITÀ ATTUALE', 'ABILITÀ POTENZIALE', 'REPUTAZIONE ATTUALE', 'REPUTAZIONE NAZIONALE', 'REPUTAZIONE MONDIALE']:
                try:
                    value = int(value) if value else None
                except ValueError:
                    pass
            new_player_data[column] = value
        
        # Aggiungi il nuovo giocatore al DataFrame
        df = pd.concat([df, pd.DataFrame([new_player_data])], ignore_index=True)
        
        # Salva il DataFrame aggiornato
        if csv_filepath:
            df.to_csv(csv_filepath, index=False)
        
        messagebox.showinfo("Successo", "Nuovo giocatore aggiunto con successo!")
        window.destroy()
        
        # Aggiorna il dropdown dei club
        update_club_dropdown()
        
        # Se il club del nuovo giocatore è quello attualmente selezionato, aggiorna la lista
        if club_var.get() and int(club_var.get()) == int(new_player_data.get('CLUB', 0)):
            show_players()
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nel salvare il nuovo giocatore: {str(e)}")

# Inizializza l'applicazione
def initialize_app():
    # Carica il mapping XML
    if not load_xml_mapping():
        return False
    
    # Carica il template XML
    if not load_template():
        return False
    
    return True

# Crea l'interfaccia grafica
root = tk.Tk()
root.title("Generatore di File XML per Calciatori - Football Manager")
root.geometry("800x600")

# Frame principale
main_frame = ttk.Frame(root, padding="10")
main_frame.pack(fill=tk.BOTH, expand=True)

# Frame per i controlli superiori
top_frame = ttk.Frame(main_frame)
top_frame.pack(fill=tk.X, pady=5)

# Pulsante per caricare il CSV
load_button = ttk.Button(top_frame, text="Carica CSV", command=load_csv)
load_button.pack(side=tk.LEFT, padx=5)

# Dropdown per selezionare il club
ttk.Label(top_frame, text="Club:").pack(side=tk.LEFT, padx=5)
club_var = tk.StringVar()
club_dropdown = ttk.Combobox(top_frame, textvariable=club_var, width=30)
club_dropdown.pack(side=tk.LEFT, padx=5)

# Pulsante per mostrare i giocatori
show_players_button = ttk.Button(top_frame, text="Mostra giocatori", command=show_players)
show_players_button.pack(side=tk.LEFT, padx=5)

# Frame per la lista dei giocatori
player_frame = ttk.LabelFrame(main_frame, text="Giocatori", padding="5")
player_frame.pack(fill=tk.BOTH, expand=True, pady=5)

# Lista dei giocatori
player_list = tk.Listbox(player_frame, selectmode=tk.MULTIPLE)
player_list.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)

# Scrollbar per la lista dei giocatori
player_scrollbar = ttk.Scrollbar(player_frame, orient=tk.VERTICAL, command=player_list.yview)
player_scrollbar.pack(fill=tk.Y, side=tk.RIGHT)
player_list.config(yscrollcommand=player_scrollbar.set)

# Frame per i pulsanti inferiori
bottom_frame = ttk.Frame(main_frame)
bottom_frame.pack(fill=tk.X, pady=5)

# Pulsante per modificare un giocatore
edit_button = ttk.Button(bottom_frame, text="Modifica giocatore", command=show_edit_window)
edit_button.pack(side=tk.LEFT, padx=5)

# Pulsante per creare un nuovo giocatore
new_player_button = ttk.Button(bottom_frame, text="Nuovo giocatore", command=create_new_player)
new_player_button.pack(side=tk.LEFT, padx=5)

# Pulsante per generare l'XML
generate_button = ttk.Button(bottom_frame, text="Genera XML", command=generate_xml)
generate_button.pack(side=tk.RIGHT, padx=5)

# Inizializza l'applicazione
if initialize_app():
    root.mainloop()
else:
    root.destroy()

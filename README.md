# Generatore di File XML per Calciatori - Football Manager

Questo strumento permette di generare file XML per Football Manager a partire da dati in formato CSV. È possibile creare, modificare e gestire i dati dei giocatori attraverso un'interfaccia grafica intuitiva.

## Requisiti

- Python 3.6 o superiore
- Librerie: tkinter, pandas, xml.etree.ElementTree

## File necessari

- `generate_giocatori.py`: Script principale dell'applicazione
- `template.xml`: Template XML per la generazione dei file
- `xml_mapping.json`: Mappatura delle proprietà dei giocatori

## Come utilizzare l'applicazione

1. Eseguire lo script `generate_giocatori.py`
2. Caricare un file CSV contenente i dati dei giocatori
3. Selezionare un club dal menu a tendina
4. Visualizzare i giocatori del club selezionato
5. Selezionare uno o più giocatori dalla lista
6. Utilizzare i pulsanti per modificare i giocatori esistenti o crearne di nuovi
7. Generare il file XML per i giocatori selezionati

## Struttura del file CSV

Il file CSV deve contenere le seguenti colonne (è incluso un file di esempio `giocatori_esempio.csv`):

- `NOME`: Nome del giocatore
- `COGNOME`: Cognome del giocatore
- `NOME COMUNE`: Nome con cui il giocatore è comunemente conosciuto
- `NOME COMPLETO`: Nome completo del giocatore
- `DATA DI NASCITA`: Data di nascita nel formato DD/MM/YYYY
- `NAZIONALITÀ`: Nazionalità del giocatore
- `CLUB`: ID del club di appartenenza
- `RUOLO`: Ruolo principale del giocatore
- `ABILITÀ ATTUALE`: Valore numerico dell'abilità attuale
- `ABILITÀ POTENZIALE`: Valore numerico dell'abilità potenziale
- `REPUTAZIONE ATTUALE`: Valore numerico della reputazione attuale
- `REPUTAZIONE NAZIONALE`: Valore numerico della reputazione nazionale
- `REPUTAZIONE MONDIALE`: Valore numerico della reputazione mondiale
- `ALTEZZA`: Altezza in centimetri
- `PESO`: Peso in chilogrammi
- `PIEDE SINISTRO`: Valore numerico dell'abilità con il piede sinistro
- `PIEDE DESTRO`: Valore numerico dell'abilità con il piede destro

Posizioni (valori da 1 a 20):
- `GK`: Portiere
- `DL`: Terzino sinistro
- `DC`: Difensore centrale
- `DR`: Terzino destro
- `WBL`: Esterno sinistro
- `WBR`: Esterno destro
- `DM`: Mediano
- `ML`: Centrocampista sinistro
- `MC`: Centrocampista centrale
- `MR`: Centrocampista destro
- `AML`: Ala sinistra
- `AMC`: Trequartista
- `AMR`: Ala destra
- `ST`: Attaccante

Statistiche nazionali:
- `NUMERO DI PRESENZE IN NAZIONALE`: Numero di presenze in nazionale
- `GOL IN NAZIONALE`: Numero di gol in nazionale

## Note importanti

- I valori delle posizioni devono essere compresi tra 1 e 20, dove 1 indica che il giocatore non è adatto a quella posizione e 20 indica che è eccellente.
- I record con valori specifici (ad esempio, GOL IN NAZIONALE = 0 o NUMERO DI PRESENZE IN NAZIONALE = 0) verranno automaticamente saltati durante la generazione dell'XML.
- Le posizioni con valore 1 verranno ignorate nella generazione dell'XML.

## Risoluzione dei problemi

Se riscontri problemi durante l'utilizzo dell'applicazione, verifica che:

1. I file `template.xml` e `xml_mapping.json` siano presenti nella stessa directory dello script
2. Il file CSV sia formattato correttamente
3. Le date siano nel formato DD/MM/YYYY
4. I valori numerici siano effettivamente numeri e non stringhe
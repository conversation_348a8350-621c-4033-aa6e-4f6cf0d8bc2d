<properties>

	<!-- all person fields -->
	<record id="pers">
		<list id="fields">
			<!-- unique id -->
			<record>
				<flags id="field" value="duni"/>
				<string id="name" value="Unique ID"/>
				<flags id="type" value="integer"/>
				<boolean id="is_read_only" value="false"/>
			</record>

			<!-- first name -->
			<record>
				<flags id="field" value="Pfna"/>
				<string id="name" value="First Name"/>
				<flags id="type" value="string"/>
				<string id="todo" value="Need to resort person name tables after all people have been modified"/>
			</record>

			<!-- second name -->
			<record>
				<flags id="field" value="Psna"/>
				<string id="name" value="Second Name"/>
				<flags id="type" value="string"/>
				<string id="todo" value="Need to resort person name tables after all people have been modified"/>
			</record>

			<!-- common name -->
			<record>
				<flags id="field" value="Pcna"/>
				<string id="name" value="Common Name"/>
				<flags id="type" value="string"/>
				<string id="todo" value="Need to resort person name tables after all people have been modified"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
			</record>

			<!-- full name -->
			<record>
				<flags id="field" value="Pfln"/>
				<string id="name" value="Full Name"/>
				<flags id="type" value="string"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
			</record>

			<!-- Combined name (used for list panel) -->
			<record>
				<flags id="field" value="dbnm"/>
				<string id="name" value="Name"/>
				<flags id="type" value="string"/>
			</record>

			<!-- person type -->
			<record>
				<flags id="field" value="Ptyp"/>
				<string id="name" value="Person Type"/>
				<flags id="type" value="enum"/>
				<list id="enum_values">
					<record enum="2" name="Player[COMMENT: person type]"/>
					<record enum="1" name="Non Player[COMMENT: person type]"/>
					<record enum="3" name="Player / Non Player[COMMENT: person type]"/>
					<record enum="4" name="Official[COMMENT: person type]"/>
					<record enum="5" name="Retired Person[COMMENT: person type]"/>
				</list>
			</record>

			<!-- female -->
			<record>
				<flags id="field" value="Pife"/>
				<string id="name" value="Female"/>
				<flags id="type" value="boolean"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="2"/>
					<record field="Ptyp" not_equals="3"/>
				</list>
			</record>

			<!-- date of birth -->
			<record>
				<flags id="field" value="Pdob"/>
				<string id="name" value="Date Of Birth"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
			</record>

			<!-- ethnicity -->
			<record>
				<flags id="field" value="Peth"/>
				<string id="name" value="Ethnicity"/>
				<flags id="type" value="enum"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
				<list id="enum_values">
					<record enum="-1" name="Unknown[COMMENT: person ethnicity]"/>
					<record enum="0" name="Northern European[COMMENT: person ethnicity]"/>
					<record enum="1" name="Mediterranean/Hispanic[COMMENT: person ethnicity]"/>
					<record enum="2" name="North African/Middle Eastern[COMMENT: person ethnicity]"/>
					<record enum="3" name="African/Caribbean[COMMENT: person ethnicity]"/>
					<record enum="4" name="Asian[COMMENT: person ethnicity]"/>
					<record enum="5" name="South East Asian[COMMENT: person ethnicity]"/>
					<record enum="6" name="Pacific Islander[COMMENT: person ethnicity]"/>
					<record enum="7" name="Native American[COMMENT: person ethnicity]"/>
					<record enum="8" name="Native Australian[COMMENT: person ethnicity]"/>
					<record enum="9" name="Mixed Race (Black/White)[COMMENT: person ethnicity]"/>
				</list>
			</record>

			<!-- hair colour -->
			<record>
				<flags id="field" value="Phcl"/>
				<string id="name" value="Hair Colour"/>
				<flags id="type" value="enum"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
				<list id="enum_values">
					<record enum="0" name="Unknown[COMMENT: person hair colour]"/>
					<record enum="1" name="Blonde[COMMENT: person hair colour]"/>
					<record enum="2" name="Light Brown[COMMENT: person hair colour]"/>
					<record enum="3" name="Dark Brown[COMMENT: person hair colour]"/>
					<record enum="4" name="Red[COMMENT: person hair colour]"/>
					<record enum="5" name="Black[COMMENT: person hair colour]"/>
					<record enum="6" name="Grey[COMMENT: person hair colour]"/>
					<record enum="7" name="Bald[COMMENT: person hair colour]"/>
				</list>
			</record>
			
			<!-- skin tone -->
			<record>
				<flags id="field" value="Pskt"/>
				<string id="name" value="Skin Tone"/>
				<flags id="type" value="enum"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
				<list id="enum_values">
					<record enum="-1" name="Unknown[COMMENT: person skin tone]"/>
					<record enum="0" name="Skin Tone 1[COMMENT: person skin tone]"/>
					<record enum="1" name="Skin Tone 2[COMMENT: person skin tone]"/>
					<record enum="2" name="Skin Tone 3[COMMENT: person skin tone]"/>
					<record enum="3" name="Skin Tone 4[COMMENT: person skin tone]"/>
					<record enum="4" name="Skin Tone 5[COMMENT: person skin tone]"/>
					<record enum="5" name="Skin Tone 6[COMMENT: person skin tone]"/>
					<record enum="6" name="Skin Tone 7[COMMENT: person skin tone]"/>
					<record enum="7" name="Skin Tone 8[COMMENT: person skin tone]"/>
					<record enum="8" name="Skin Tone 9[COMMENT: person skin tone]"/>
					<record enum="9" name="Skin Tone 10[COMMENT: person skin tone]"/>
					<record enum="10" name="Skin Tone 11[COMMENT: person skin tone]"/>
					<record enum="11" name="Skin Tone 12[COMMENT: person skin tone]"/>
					<record enum="12" name="Skin Tone 13[COMMENT: person skin tone]"/>
					<record enum="13" name="Skin Tone 14[COMMENT: person skin tone]"/>
					<record enum="14" name="Skin Tone 15[COMMENT: person skin tone]"/>
					<record enum="15" name="Skin Tone 16[COMMENT: person skin tone]"/>
					<record enum="16" name="Skin Tone 17[COMMENT: person skin tone]"/>
					<record enum="17" name="Skin Tone 18[COMMENT: person skin tone]"/>
					<record enum="18" name="Skin Tone 19[COMMENT: person skin tone]"/>
					<record enum="19" name="Skin Tone 20[COMMENT: person skin tone]"/>
				</list>
			</record>
			
			<!-- skin tone colour, viewing only -->
			<record>
				<flags id="field" value="Pske"/>
				<string id="name" value="Skin Tone Example"/>
				<flags id="type" value="colour"/>
			</record>
			
			<!-- age, viewing only -->
			<record>
				<flags id="field" value="Page"/>
				<string id="name" value="Age"/>
				<flags id="type" value="integer"/>
			</record>

			<!-- city of birth -->
			<record>
				<flags id="field" value="Pcbi"/>
				<string id="name" value="City Of Birth"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="city"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
			</record>

			<!-- nation -->
			<record>
				<flags id="field" value="Pnti"/>
				<string id="name" value="Nation"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="nation"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
				</list>
			</record>

			<!-- division -->
			<record>
				<flags id="field" value="Pdvi"/>
				<string id="name" value="Division"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="competition"/>
				<boolean id="is_read_only" value="false"/>
				<boolean id="is_optional" value="true"/>
			</record>
			
			<!-- declared for nation (player only) -->
			<record>
				<flags id="field" value="Pdfn"/>
				<string id="name" value="Declared For Nation"/>
				<flags id="type" value="boolean"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="1"/>
					<record field="Ptyp" not_equals="4"/>
					<record field="Ptyp" not_equals="5"/>
				</list>
			</record>

			<!-- international retirement (player only) -->
			<record>
				<flags id="field" value="Prid"/>
				<string id="name" value="International Retirement"/>
				<flags id="type" value="boolean"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="1"/>
					<record field="Ptyp" not_equals="4"/>
					<record field="Ptyp" not_equals="5"/>
				</list>
			</record>

			<!-- international retirement date (player only) -->
			<record>
				<flags id="field" value="Pird"/>
				<string id="name" value="International Retirement Date"/>
				<flags id="type" value="date"/>
			</record>

			<!-- slow retirement -->
			<record>
				<flags id="field" value="Pslr"/>
				<string id="name" value="Retiring After Spell At Current Club[COMMENT: editor field for person]"/>
				<flags id="type" value="boolean"/>
			</record>
			
			<!-- second nations -->
			<record>
				<flags id="field" value="P2ni"/>
				<string id="name" value="Second Nations"/>
				<flags id="type" value="database_record_list"/>
				<flags id="database_table_type" value="nation"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="4"/>
				</list>
			</record>

			<!-- International Apps -->
			<record>
				<flags id="field" value="Piap"/>
				<string id="name" value="International Apps"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="255"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
					<record field="Ptyp" not_equals="4"/>
				</list>
			</record>

			<!-- International Goals-->
			<record>
				<flags id="field" value="Pigl"/>
				<string id="name" value="International Goals"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="255"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
					<record field="Ptyp" not_equals="4"/>
				</list>
			</record>

			<!-- U21 International Apps-->
			<record>
				<flags id="field" value="Puia"/>
				<string id="name" value="U21 International Apps"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="255"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
					<record field="Ptyp" not_equals="4"/>
				</list>
			</record>

			<!-- U21 International Goals-->
			<record>
				<flags id="field" value="Puig"/>
				<string id="name" value="U21 International Goals"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="255"/>
				<list id="dependencies">
					<record field="Ptyp" not_equals="5"/>
					<record field="Ptyp" not_equals="4"/>
				</list>
			</record>

			<!-- club -->
			<record>
				<flags id="field" value="Pcti"/>
				<string id="name" value="Club"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="team"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- club job -->
			<record>
				<flags id="field" value="Pcjt"/>
				<string id="name" value="Job"/>
				<flags id="type" value="enum"/>
				<flags id="enum_values" value="jobs"/>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

      <!-- Caretaker -->
      <record>
        <flags id="field" value="Pcrt"/>
        <string id="name" value="Caretaker Manager"/>
        <flags id="type" value="boolean"/>
        <list id="dependencies">
          <record field="Ptyp" not_equals="2"/>
          <record field="Ptyp" not_equals="4"/>
          <record field="Ptyp" not_equals="5"/>
          <record field="Pcti" exists="true"/>
        </list>
      </record>

      <!-- date joined -->
			<record>
				<flags id="field" value="Pcjd"/>
				<string id="name" value="Date Joined"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

			<!-- contract started -->
			<record>
				<flags id="field" value="Pcsd"/>
				<string id="name" value="Contract Started"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

			<!-- contract expires -->
			<record>
				<flags id="field" value="Pcex"/>
				<string id="name" value="Contract Expires"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

			<!-- status -->
			<record>
				<flags id="field" value="Pcst"/>
				<string id="name" value="Contract Type"/>
				<flags id="type" value="enum"/>
				<list id="enum_values">
					<record enum="1" name="Full Time[COMMENT: contract type]"/>
					<record enum="0" name="Part Time[COMMENT: contract type]"/>
					<record enum="2" name="Amateur[COMMENT: contract type]"/>
					<record enum="3" name="Youth[COMMENT: contract type]"/>
				</list>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

			<!-- weekly wage-->
			<record>
				<flags id="field" value="Pcwg"/>
				<string id="name" value="Weekly Wage"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="300000"/>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

			<!-- squad number -->
			<record>
				<flags id="field" value="Psnu"/>
				<string id="name" value="Squad Number"/>
				<flags id="type" value="integer"/>
				<boolean id="is_optional" value="true"/>
				<integer id="min_value" value="1"/>
				<integer id="max_value" value="99"/>
				<!-- dont use dependency here so that it works in a player list table -->
				<!-- <list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list> -->
			</record>

			<!-- preferred squad number -->
			<record>
				<flags id="field" value="Ppsq"/>
				<string id="name" value="Preferred Squad Number"/>
				<flags id="type" value="integer"/>
				<boolean id="is_optional" value="true"/>
				<integer id="min_value" value="1"/>
				<integer id="max_value" value="99"/>
			</record>

			<!-- Coowned By -->
			<record>
				<flags id="field" value="Pcoi"/>
				<string id="name" value="Co-owned By"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="club"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

			<!-- Coowned Wage -->
			<record>
				<flags id="field" value="Pcow"/>
				<string id="name" value="Co-own Wage"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="300000"/>
				<list id="dependencies">
					<record field="Pcoi" exists="true"/>
				</list>
				<list id="dependencies">
					<record field="Pcti" exists="true"/>
				</list>
			</record>

			<!-- Appearance Bonus -->
			<record>
				<flags id="field" value="Pcap"/>
				<string id="name" value="Appearance Bonus"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="100000"/>
			</record>

			<!-- Goal Bonus -->
			<record>
				<flags id="field" value="Pcgb"/>
				<string id="name" value="Goal Bonus"/>
				<flags id="type" value="cash"/>
				<integer id="min_value" value="1"/>
				<integer id="max_value" value="100000"/>
			</record>

			<!-- Clean Sheet Bonus -->
			<record>
				<flags id="field" value="Pccb"/>
				<string id="name" value="Clean Sheet Bonus"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="100000"/>
			</record>

			<!-- Promotion Wage Increase -->
			<record>
				<flags id="field" value="Pcpi"/>
				<string id="name" value="Promotion Wage Increase"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="100"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Yearly Wage Increase -->
			<record>
				<flags id="field" value="Pywi"/>
				<string id="name" value="Yearly Wage Increase"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="100"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Minimum Fee Release -->
			<record>
				<flags id="field" value="Pmfr"/>
				<string id="name" value="Minimum Fee Release"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="1000000000"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Relegation Release Fee -->
			<record>
				<flags id="field" value="Prrf"/>
				<string id="name" value="Relegation Release Fee"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="1000000000"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Non Promotion Release Fee -->
			<record>
				<flags id="field" value="Pnpr"/>
				<string id="name" value="Non Promotion Release Fee"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="1000000000"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Will Leave At End Of Contract -->
			<record>
				<flags id="field" value="Plec"/>
				<string id="name" value="Will Leave At End Of Contract"/>
				<flags id="type" value="boolean"/>
			</record>

			<!-- MLS player type -->
			<record>
				<flags id="field" value="Pmpt"/>
				<string id="name" value="MLS Player Type[COMMENT: editor; status of player for American Major League]"/>
				<flags id="type" value="enum"/>
				<boolean id="is_optional" value="true"/>
				<list id="enum_values">
					<record enum="5" name="Future Professional[COMMENT: mls player status type]"/>
					<record enum="6" name="Discovery Player[COMMENT: mls player status type]"/>
					<record enum="7" name="Generation Adidas[COMMENT: mls player status type]"/>
					<record enum="8" name="Senior Developmental Contract[COMMENT: person screen; transfer section; type of contract for an MLS player]"/>
					<record enum="9" name="Developmental Contract[COMMENT: person screen; transfer section; type of contract for an MLS player]"/>
					<record enum="11" name="Designated Player Contract[COMMENT: person screen; transfer section; type of contract for an MLS player]"/>
				</list>
			</record>

			<!-- MLS rights club -->
			<record>
				<flags id="field" value="Pmrg"/>
				<string id="name" value="MLS Rights Club[COMMENT: editor; the MLS club which owns the rights of this player]"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="club"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- MLS rights expiry date -->
			<record>
				<flags id="field" value="Pmrd"/>
				<string id="name" value="MLS Rights Expiry Date[COMMENT: editor; the expiry date of the club rights for this player]"/>
				<flags id="type" value="date"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- MLS draft type -->
			<record>
				<flags id="field" value="Pmdt"/>
				<string id="name" value="MLS Draft Type[COMMENT: editor; the expiry date of the club rights for this player]"/>
				<flags id="type" value="enum"/>
				<boolean id="is_optional" value="true"/>
				<list id="enum_values">
					<record enum="2" name="Inaugural[COMMENT: mls player draft type]"/>
					<record enum="0" name="Superdraft[COMMENT: mls player draft type]"/>
					<record enum="4" name="Dispersal[COMMENT: mls player draft type]"/>
					<record enum="1" name="Supplemental[COMMENT: mls player draft type]"/>
					<record enum="7" name="Draft Lottery[COMMENT: mls player draft type]"/>
					<record enum="8" name="Allocation[COMMENT: mls player draft type]"/>
				</list>
			</record>

			<!-- MLS guaranteed deal -->
			<record>
				<flags id="field" value="Pmgp"/>
				<string id="name" value="MLS Guaranteed Deal - Player[COMMENT: editor; does the player have a guaranteed deal with an MLS club (from player side)]"/>
				<flags id="type" value="boolean"/>
			</record>

			<!-- MLS guaranteed deal -->
			<record>
				<flags id="field" value="Pmgc"/>
				<string id="name" value="MLS Guaranteed Deal - Club[COMMENT: editor; does the player have a guaranteed deal with an MLS club (from club side)]"/>
				<flags id="type" value="boolean"/>
			</record>
			
			<!-- MLS Discovery player date  -->
			<record>
				<flags id="field" value="Pmdd"/>
				<string id="name" value="MLS Discovery Player Date[COMMENT: editor; the date this player was signed as an MLS discovery player]"/>
				<flags id="type" value="date"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- MLS Last Draft Club -->
			<record>
				<flags id="field" value="Pmlc"/>
				<string id="name" value="MLS Last Draft Club[COMMENT: editor; the MLS club this player was last drafted to]"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="club"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- MLS Last Draft Year -->
			<record>
				<flags id="field" value="Pmly"/>
				<string id="name" value="MLS Last Draft Year[COMMENT: editor; the year this player was last drafted in]"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="1970"/>
				<integer id="max_value" value="3000"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- MLS Last Draft Round -->
			<record>
				<flags id="field" value="Pmlr"/>
				<string id="name" value="MLS Last Draft Round[COMMENT: editor; the round this player was last drafted in]"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="1"/>
				<integer id="max_value" value="50"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- MLS Last Draft Pick -->
			<record>
				<flags id="field" value="Pmlp"/>
				<string id="name" value="MLS Last Draft Pick[COMMENT: editor; the order this player was last drafted in]"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="1"/>
				<integer id="max_value" value="200"/>
				<boolean id="is_optional" value="true"/>
			</record>
			
			<!-- Nation -->
			<record>
				<flags id="field" value="Pnai"/>
				<string id="name" value="Nation"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="nation"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Nation Job -->
			<record>
				<flags id="field" value="Pnjo"/>
				<string id="name" value="Job"/>
				<flags id="type" value="enum"/>
				<flags id="enum_values" value="npjs"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="Pnai" exists="true"/>
				</list>
			</record>

			<!-- Nation - Date Joined -->
			<record>
				<flags id="field" value="Pnjd"/>
				<string id="name" value="Date Joined"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="Pnai" exists="true"/>
				</list>
			</record>

			<!-- Nation - Contract Started -->
			<record>
				<flags id="field" value="Pnsd"/>
				<string id="name" value="Contract Started"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="Pnai" exists="true"/>
					<record field="Pnjo" equals="16"/>
				</list>
			</record>

			<!-- Nation - Contract Expires -->
			<record>
				<flags id="field" value="Pnex"/>
				<string id="name" value="Contract Expires"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="Pnai" exists="true"/>
					<record field="Pnjo" equals="16"/>
				</list>
			</record>

			<!-- Nation - Weekly Wage -->
			<record>
				<flags id="field" value="Pnww"/>
				<string id="name" value="Weekly Wage"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="200000"/>
				<list id="dependencies">
					<record field="Pnai" exists="true"/>
					<record field="Pnjo" equals="16"/>
				</list>
			</record>

			<!-- Loan Club -->
			<record>
				<flags id="field" value="PLcl"/>
				<string id="name" value="Club"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="team"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Loan - Start Date -->
			<record>
				<flags id="field" value="PLsd"/>
				<string id="name" value="Start Date"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="PLcl" exists="true"/>
				</list>
			</record>

			<!-- Loan - End Date -->
			<record>
				<flags id="field" value="PLed"/>
				<string id="name" value="End Date"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="PLcl" exists="true"/>
				</list>
			</record>

			<!-- Loan - Can Be Recalled -->
			<record>
				<flags id="field" value="PLbr"/>
				<string id="name" value="Can Be Recalled"/>
				<flags id="type" value="boolean"/>
				<list id="dependencies">
					<record field="PLcl" exists="true"/>
				</list>
			</record>

			<!-- Loan - squad number -->
			<record>
				<flags id="field" value="PLsn"/>
				<string id="name" value="Squad Number"/>
				<flags id="type" value="integer"/>
				<boolean id="is_optional" value="true"/>
				<integer id="min_value" value="1"/>
				<integer id="max_value" value="99"/>
				<list id="dependencies">
					<record field="PLcl" exists="true"/>
				</list>
			</record>

			<!-- Loan - Fee To Buy -->
			<record>
				<flags id="field" value="PLfb"/>
				<string id="name" value="Fee To Buy"/>
				<flags id="type" value="cash"/>
				<integer id="default_value" value="10000"/>
				<integer id="min_value" value="-1"/>
				<integer id="max_value" value="1000000000"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="PLcl" exists="true"/>
				</list>
			</record>

			<!-- Loan - Wage Contribution -->
			<record>
				<flags id="field" value="PLwc"/>
				<string id="name" value="% Wages Paid By Loaning Club"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="100"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="PLcl" exists="true"/>
				</list>
			</record>

			<!-- Future Transfer - Club -->
			<record>
				<flags id="field" value="PFcl"/>
				<string id="name" value="Club"/>
				<flags id="type" value="database_record_unique_id"/>
				<flags id="database_table_type" value="team"/>
				<boolean id="is_optional" value="true"/>
			</record>

			<!-- Future Transfer - Transfer Date -->
			<record>
				<flags id="field" value="PFdt"/>
				<string id="name" value="Transfer Date"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="PFcl" exists="true"/>
				</list>
			</record>

			<!-- Future Transfer - Contract End Date -->
			<record>
				<flags id="field" value="PFed"/>
				<string id="name" value="Contract End Date"/>
				<flags id="type" value="date"/>
				<list id="dependencies">
					<record field="PFcl" exists="true"/>
				</list>
			</record>

			<!-- Future Transfer - Transfer Fee-->
			<record>
				<flags id="field" value="PFtf"/>
				<string id="name" value="Transfer Fee"/>
				<flags id="type" value="cash"/>
				<integer id="max_value" value="1000000000"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="PFcl" exists="true"/>
				</list>
			</record>

			<!-- Future Transfer - Wage -->
			<record>
				<flags id="field" value="PFwa"/>
				<string id="name" value="Wage"/>
				<flags id="type" value="cash"/>
				<boolean id="is_optional" value="true"/>
				<integer id="max_value" value="200000"/>
				<boolean id="is_optional" value="true"/>
				<list id="dependencies">
					<record field="PFcl" exists="true"/>
				</list>
			</record>

			<!-- Future Transfer - New Job -->
			<record>
				<flags id="field" value="PFjo"/>
				<string id="name" value="New Job"/>
				<flags id="type" value="enum"/>
				<flags id="enum_values" value="ftjs"/>
				<list id="dependencies">
					<record field="PFcl" exists="true"/>
				</list>
			</record>

			<!-- Person Data - Adaptability -->
			<record>
				<flags id="field" value="Pada"/>
				<string id="name" value="Adaptability"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Person Data - Ambition -->
			<record>
				<flags id="field" value="Pamb"/>
				<string id="name" value="Ambition"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Person Data - Controversy -->
			<record>
				<flags id="field" value="Pcvy"/>
				<string id="name" value="Controversy"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Person Data - Loyalty -->
			<record>
				<flags id="field" value="Ploy"/>
				<string id="name" value="Loyalty"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Person Data - Pressure -->
			<record>
				<flags id="field" value="Ppre"/>
				<string id="name" value="Pressure"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Person Data - Professionalism -->
			<record>
				<flags id="field" value="Ppro"/>
				<string id="name" value="Professionalism"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Person Data - Sportsmanship -->
			<record>
				<flags id="field" value="Pspo"/>
				<string id="name" value="Sportsmanship"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Person Data - Temperament -->
			<record>
				<flags id="field" value="Ptem"/>
				<string id="name" value="Temperament"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Job Preferences - Assistant Manager -->
			<record>
				<flags id="field" value="PPam"/>
				<string id="name" value="Assistant Manager"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Job Preferences - Coach -->
			<record>
				<flags id="field" value="PPco"/>
				<string id="name" value="Coach"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Job Preferences - Fitness Coach -->
			<record>
				<flags id="field" value="PPfc"/>
				<string id="name" value="Fitness Coach"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Job Preferences - Gk Coach -->
			<record>
				<flags id="field" value="PPgk"/>
				<string id="name" value="Goalkeeping Coach"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Job Preferences - Manager -->
			<record>
				<flags id="field" value="PPmn"/>
				<string id="name" value="Manager"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Job Preferences - Physio -->
			<record>
				<flags id="field" value="PPph"/>
				<string id="name" value="Physio"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Job Preferences - Scout -->
			<record>
				<flags id="field" value="PPsc"/>
				<string id="name" value="Scout"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- languages -->
			<record>
				<flags id="field" value="Plgl"/>
				<string id="name" value="Languages"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- language -->
						<record>
							<flags id="field" value="lang"/>
							<string id="name" value="Language"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="language"/>
						</record>

						<!-- Proficiency -->
						<record>
							<flags id="field" value="prof"/>
							<string id="name" value="Proficiency"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="20"/>
							<list id="dependencies">
								<record field="csln" equals="false"/>
							</list>
						</record>

						<!-- Cant speak language -->
						<record>
							<flags id="field" value="csln"/>
							<string id="name" value="Cannot Speak Language"/>
							<flags id="type" value="boolean"/>
						</record>
					</list>
				</record>
			</record>

			<!-- player fields -->
			<record insert_fields="play"/>

			<!-- non player fields -->
			<record insert_fields="nnpl"/>

			<!-- official fields -->
			<record insert_fields="offc"/>
			
			<!-- Favourite People -->
			<record>
				<flags id="field" value="Pfvp"/>
				<string id="name" value="Favourite People"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- Club -->
						<record>
							<flags id="field" value="pers"/>
							<string id="name" value="Person"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="person"/>
						</record>

						<!-- Level -->
						<record>
							<flags id="field" value="leve"/>
							<string id="name" value="Level"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="100"/>
						</record>

            <!-- permanent -->
            <record>
              <flags id="field" value="perm"/>
              <string id="name" value="Permanent"/>
              <flags id="type" value="boolean"/>
            </record>
          </list>
				</record>
			</record>

			<!-- Disliked People -->
			<record>
				<flags id="field" value="Pdsp"/>
				<string id="name" value="Disliked People"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- Club -->
						<record>
							<flags id="field" value="pers"/>
							<string id="name" value="Person"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="person"/>
						</record>

						<!-- Level -->
						<record>
							<flags id="field" value="leve"/>
							<string id="name" value="Level"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="100"/>
						</record>

            <!-- permanent -->
            <record>
              <flags id="field" value="perm"/>
              <string id="name" value="Permanent"/>
              <flags id="type" value="boolean"/>
            </record>
          </list>
				</record>
			</record>

			<!-- Favourite Clubs -->
			<record>
				<flags id="field" value="Pfvc"/>
				<string id="name" value="Favourite Clubs"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- Club -->
						<record>
							<flags id="field" value="club"/>
							<string id="name" value="Club"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="club"/>
						</record>

						<!-- Level -->
						<record>
							<flags id="field" value="leve"/>
							<string id="name" value="Level"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="100"/>
						</record>
					</list>
				</record>
			</record>

			<!-- Disliked Clubs -->
			<record>
				<flags id="field" value="Pdsc"/>
				<string id="name" value="Disliked Clubs"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- Club -->
						<record>
							<flags id="field" value="club"/>
							<string id="name" value="Club"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="club"/>
						</record>

						<!-- Level -->
						<record>
							<flags id="field" value="leve"/>
							<string id="name" value="Level"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="100"/>
						</record>
					</list>
				</record>
			</record>

			<!-- playing history list -->
			<record>
				<flags id="field" value="Plhs"/>
				<string id="name" value="Playing History"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- Year -->
						<record>
							<flags id="field" value="year"/>
							<string id="name" value="Year"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="1900"/>
							<integer id="max_value" value="2100"/>
						</record>

						<!-- Year order -->
						<record>
							<flags id="field" value="yror"/>
							<string id="name" value="Order"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="10"/>
						</record>
						
						<!-- Club -->
						<record>
							<flags id="field" value="clid"/>
							<string id="name" value="Club"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="team"/>
						</record>

						<!-- Division -->
						<record>
							<flags id="field" value="divi"/>
							<string id="name" value="Division"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="competition"/>
							<boolean id="is_optional" value="true"/>
						</record>

						<!-- On Loan -->
						<record>
							<flags id="field" value="onln"/>
							<string id="name" value="On Loan"/>
							<flags id="type" value="boolean"/>
						</record>

						<!-- Youth team -->
						<record>
							<flags id="field" value="ytht"/>
							<string id="name" value="Youth Team"/>
							<flags id="type" value="boolean"/>
						</record>

						<!-- Apps -->
						<record>
							<flags id="field" value="apps"/>
							<string id="name" value="Apps[COMMENT: Competition editor; field name; appearances]"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="200"/>
							<boolean id="is_optional" value="true"/>
						</record>

						<!-- Goals -->
						<record>
							<flags id="field" value="gols"/>
							<string id="name" value="Goals"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="200"/>
							<boolean id="is_optional" value="true"/>
						</record>

						<!-- Transfer Fee -->
						<record>
							<flags id="field" value="cash"/>
							<string id="name" value="Transfer Fee"/>
							<flags id="type" value="cash"/>
							<integer id="max_value" value="1000000000"/>
							<boolean id="is_optional" value="true"/>
						</record>
					</list>
				</record>
			</record>

			<!-- non playing history list -->
			<record>
				<flags id="field" value="Pnph"/>
				<string id="name" value="Non Playing History"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- Club -->
						<record>
							<flags id="field" value="team"/>
							<string id="name" value="Club"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="team"/>
						</record>

						<!-- Date Joined -->
						<record>
							<flags id="field" value="join"/>
							<string id="name" value="Date Joined"/>
							<flags id="type" value="date"/>
							<boolean id="allow_just_year" value="true"/>
						</record>

						<!-- Date Left -->
						<record>
							<flags id="field" value="left"/>
							<string id="name" value="Date Left"/>
							<flags id="type" value="date"/>
							<boolean id="allow_just_year" value="true"/>
						</record>

						<!-- Job -->
						<record>
							<flags id="field" value="tjob"/>
							<string id="name" value="Job[COMMENT: Competition editor; field name; appearances]"/>
							<flags id="type" value="enum"/>
							<flags id="enum_values" value="jobs"/>
						</record>
					</list>
				</record>
			</record>

			<!-- achievements -->
			<record>
				<flags id="field" value="Pacl"/>
				<string id="name" value="Achievements"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- Club -->
						<record>
							<flags id="field" value="team"/>
							<string id="name" value="Club"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="team"/>
							<boolean id="is_optional" value="true"/>
							<list id="dependencies">
								<record field="nati" exists="false"/>
							</list>
						</record>

						<!-- Nation -->
						<record>
							<flags id="field" value="nati"/>
							<string id="name" value="Nation"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="nation"/>
							<boolean id="is_optional" value="true"/>
							<list id="dependencies">
								<record field="team" exists="false"/>
							</list>
						</record>

						<!-- Competition -->
						<record>
							<flags id="field" value="comp"/>
							<string id="name" value="Competition"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="competition"/>
							<boolean id="is_optional" value="true"/>
						</record>

						<!-- Placing -->
						<record>
							<flags id="field" value="plac"/>
							<string id="name" value="Placing"/>
							<flags id="type" value="enum"/>
							<list id="enum_values">
								<record enum="1" name="Winner[COMMENT: person achievement; placing]"/>
								<record enum="2" name="Runner-Up[COMMENT: person achievement; placing]"/>
								<record enum="3" name="Third Place[COMMENT: person achievement; placing]"/>
								<record enum="4" name="Fourth Place[COMMENT: person achievement; placing]"/>
								<record enum="5" name="Playoff Winner[COMMENT: person achievement; placing]"/>
							</list>
						</record>

						<!-- Date -->
						<record>
							<flags id="field" value="date"/>
							<string id="name" value="Date"/>
							<flags id="type" value="date"/>
							<boolean id="is_optional" value="true"/>
							<flags id="table_widget" value=""/>
						</record>

						<!-- Year -->
						<record>
							<flags id="field" value="year"/>
							<string id="name" value="Year"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="1900"/>
							<integer id="max_value" value="2100"/>
							<boolean id="is_optional" value="true"/>
							<flags id="table_widget" value=""/>
						</record>

						<!-- Job -->
						<record>
							<flags id="field" value="ajob"/>
							<string id="name" value="Job"/>
							<flags id="type" value="enum"/>
							<flags id="enum_values" value="jobs"/>
							<boolean id="is_optional" value="true"/>
							<flags id="table_widget" value=""/>
						</record>
					</list>
				</record>
			</record>
		</list>
	</record>
</properties>

<properties>

	<!-- all player fields -->
	<record id="play">
		<list id="fields">
			
			<!-- Player - Current Ability -->
			<record>
				<flags id="field" value="PCAB"/>
				<string id="name" value="Current Ability"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="200"/>
			</record>

			<!-- Player - Potential Ability -->
			<record>
				<flags id="field" value="PPAB"/>
				<string id="name" value="Potential Ability"/>
				<flags id="type" value="integer"/>
				<integer id="default_value" value="0"/>
				<integer id="min_value" value="-10"/>
				<integer id="max_value" value="200"/>
			</record>

			<!-- Player - Current Reputation-->
			<record>
				<flags id="field" value="PCRP"/>
				<string id="name" value="Current Reputation"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="200"/>
			</record>

			<!-- Player - Home Reputation-->
			<record>
				<flags id="field" value="PHRP"/>
				<string id="name" value="Home Reputation"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="200"/>
			</record>

			<!-- Player - World Reputation-->
			<record>
				<flags id="field" value="PWRP"/>
				<string id="name" value="World Reputation"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="200"/>
			</record>
			
			<!-- Player - Goalkeeper-->
			<record>
				<flags id="field" value="Pgoa"/>
				<string id="name" value="Goalkeeper"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Player - Sweeper -->
			<record>
				<flags id="field" value="Pswe"/>
				<string id="name" value="Sweeper"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Player - Defender Left -->
			<record>
				<flags id="field" value="Pdls"/>
				<string id="name" value="Defender Left"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Defender Central-->
			<record>
				<flags id="field" value="Pdce"/>
				<string id="name" value="Defender Central"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Defender Right -->
			<record>
				<flags id="field" value="Pdrs"/>
				<string id="name" value="Defender Right"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Defensive Midfielder -->
			<record>
				<flags id="field" value="Pdmc"/>
				<string id="name" value="Defensive Midfielder"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Wing Back Left -->
			<record>
				<flags id="field" value="Pwbl"/>
				<string id="name" value="Wing Back Left"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Wing Back Right -->
			<record>
				<flags id="field" value="Pwbr"/>
				<string id="name" value="Wing Back Right"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Midfielder Left -->
			<record>
				<flags id="field" value="Pmls"/>
				<string id="name" value="Midfielder Left"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Midfielder Central -->
			<record>
				<flags id="field" value="Pmce"/>
				<string id="name" value="Midfielder Central"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Midfielder Right -->
			<record>
				<flags id="field" value="Pmrs"/>
				<string id="name" value="Midfielder Right"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Attacking Midfielder Left -->
			<record>
				<flags id="field" value="Paml"/>
				<string id="name" value="Attacking Midfielder Left"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Attacking Midfielder Central -->
			<record>
				<flags id="field" value="Pamc"/>
				<string id="name" value="Attacking Midfielder Central"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Attacking Midfielder Right -->
			<record>
				<flags id="field" value="Pamr"/>
				<string id="name" value="Attacking Midfielder Right"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Attacker -->
			<record>
				<flags id="field" value="Pace"/>
				<string id="name" value="Attacker"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Free Role -->
			<record>
				<flags id="field" value="Pfre"/>
				<string id="name" value="Free Role"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Player - Aggression -->
			<record>
				<flags id="field" value="PDag"/>
				<string id="name" value="Aggression"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Anticipation -->
			<record>
				<flags id="field" value="PDan"/>
				<string id="name" value="Anticipation"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Bravery -->
			<record>
				<flags id="field" value="PDbr"/>
				<string id="name" value="Bravery"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Composure -->
			<record>
				<flags id="field" value="PDcp"/>
				<string id="name" value="Composure"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Concentration -->
			<record>
				<flags id="field" value="PDcc"/>
				<string id="name" value="Concentration"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Consistency -->
			<record>
				<flags id="field" value="PDcy"/>
				<string id="name" value="Consistency"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Creativity -->
			<record>
				<flags id="field" value="PDvs"/>
				<string id="name" value="Creativity"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Decisions -->
			<record>
				<flags id="field" value="PDdc"/>
				<string id="name" value="Decisions"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Determination -->
			<record>
				<flags id="field" value="PDde"/>
				<string id="name" value="Determination"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Dirtiness -->
			<record>
				<flags id="field" value="PDdt"/>
				<string id="name" value="Dirtiness"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Flair -->
			<record>
				<flags id="field" value="PDfl"/>
				<string id="name" value="Flair"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Important Matches -->
			<record>
				<flags id="field" value="PDim"/>
				<string id="name" value="Important Matches"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Influence -->
			<record>
				<flags id="field" value="PDld"/>
				<string id="name" value="Influence"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Off The Ball -->
			<record>
				<flags id="field" value="PDmv"/>
				<string id="name" value="Off The Ball"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Positioning -->
			<record>
				<flags id="field" value="PDpt"/>
				<string id="name" value="Positioning"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Team Work -->
			<record>
				<flags id="field" value="PDtm"/>
				<string id="name" value="Team Work"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Work Rate -->
			<record>
				<flags id="field" value="PDwr"/>
				<string id="name" value="Work Rate"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Acceleration -->
			<record>
				<flags id="field" value="PDac"/>
				<string id="name" value="Acceleration"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Agility -->
			<record>
				<flags id="field" value="PDal"/>
				<string id="name" value="Agility"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Balance -->
			<record>
				<flags id="field" value="PDbl"/>
				<string id="name" value="Balance"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Injury Proneness -->
			<record>
				<flags id="field" value="PDij"/>
				<string id="name" value="Injury Proneness"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Jumping -->
			<record>
				<flags id="field" value="PDjm"/>
				<string id="name" value="Jumping"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Natural Fitness -->
			<record>
				<flags id="field" value="PDnf"/>
				<string id="name" value="Natural Fitness"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Pace -->
			<record>
				<flags id="field" value="PDpc"/>
				<string id="name" value="Pace"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Stamina -->
			<record>
				<flags id="field" value="PDsm"/>
				<string id="name" value="Stamina"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Strength -->
			<record>
				<flags id="field" value="PDst"/>
				<string id="name" value="Strength"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Height -->
			<record>
				<flags id="field" value="Phes"/>
				<string id="name" value="Height"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="210"/>
			</record>

			<!-- Player - Weight -->
			<record>
				<flags id="field" value="Pwes"/>
				<string id="name" value="Weight"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="200"/>
			</record>

			<!-- Player - Corners -->
			<record>
				<flags id="field" value="PDcs"/>
				<string id="name" value="Corners"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>

			<!-- Player - Crossing -->
			<record>
				<flags id="field" value="PDcr"/>
				<string id="name" value="Crossing"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Dribbling -->
			<record>
				<flags id="field" value="PDdr"/>
				<string id="name" value="Dribbling"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Finishing -->
			<record>
				<flags id="field" value="PDfn"/>
				<string id="name" value="Finishing"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - First Touch -->
			<record>
				<flags id="field" value="PDft"/>
				<string id="name" value="First Touch"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Free Kicks -->
			<record>
				<flags id="field" value="PDfk"/>
				<string id="name" value="Free Kicks"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Heading -->
			<record>
				<flags id="field" value="PDhd"/>
				<string id="name" value="Heading"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Long Shots -->
			<record>
				<flags id="field" value="PDls"/>
				<string id="name" value="Long Shots"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Long Throws -->
			<record>
				<flags id="field" value="PDlt"/>
				<string id="name" value="Long Throws"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Marking -->
			<record>
				<flags id="field" value="PDmr"/>
				<string id="name" value="Marking"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Passing -->
			<record>
				<flags id="field" value="PDps"/>
				<string id="name" value="Passing"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Penalty Taking -->
			<record>
				<flags id="field" value="PDpn"/>
				<string id="name" value="Penalty Taking"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Tackling -->
			<record>
				<flags id="field" value="PDtk"/>
				<string id="name" value="Tackling"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Technique -->
			<record>
				<flags id="field" value="PDtc"/>
				<string id="name" value="Technique"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Versatility -->
			<record>
				<flags id="field" value="PDvt"/>
				<string id="name" value="Versatility"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Aerial Ability -->
			<record>
				<flags id="field" value="PDae"/>
				<string id="name" value="Aerial Ability"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Command Of Area -->
			<record>
				<flags id="field" value="PDca"/>
				<string id="name" value="Command Of Area"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Communication -->
			<record>
				<flags id="field" value="PDcm"/>
				<string id="name" value="Communication"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Eccentricity -->
			<record>
				<flags id="field" value="PDec"/>
				<string id="name" value="Eccentricity"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Handling -->
			<record>
				<flags id="field" value="PDhn"/>
				<string id="name" value="Handling"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Kicking -->
			<record>
				<flags id="field" value="PDkc"/>
				<string id="name" value="Kicking"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - One On Ones -->
			<record>
				<flags id="field" value="PDoo"/>
				<string id="name" value="One On Ones"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Reflexes -->
			<record>
				<flags id="field" value="PDrf"/>
				<string id="name" value="Reflexes"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Rushing Out -->
			<record>
				<flags id="field" value="PDro"/>
				<string id="name" value="Rushing Out"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Tendency To Punch -->
			<record>
				<flags id="field" value="PDtp"/>
				<string id="name" value="Tendency To Punch"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Throwing -->
			<record>
				<flags id="field" value="PDth"/>
				<string id="name" value="Throwing"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Left Foot -->
			<record>
				<flags id="field" value="PDlf"/>
				<string id="name" value="Left Foot"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player - Right Foot -->
			<record>
				<flags id="field" value="PDri"/>
				<string id="name" value="Right Foot"/>
				<flags id="type" value="integer"/>
				<integer id="min_value" value="0"/>
				<integer id="max_value" value="20"/>
			</record>
			
			<!-- Player Preferred Moves-->
			<record>
				<flags id="field" value="PMrl"/>
				<string id="name" value="Runs With Ball Down Left"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMrr"/>
				<string id="name" value="Runs With Ball Down Right"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMrc"/>
				<string id="name" value="Runs With Ball Through The Centre"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMoa"/>
				<string id="name" value="Gets Into Opposition Area"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMmc"/>
				<string id="name" value="Moves Into Channels"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMgf"/>
				<string id="name" value="Gets Forward Whenever Possible"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMss"/>
				<string id="name" value="Plays Short Simple Passes"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMkb"/>
				<string id="name" value="Tries Killer Balls Often"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMsl"/>
				<string id="name" value="Shoots Long"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMsp"/>
				<string id="name" value="Shoots With Power"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMps"/>
				<string id="name" value="Places Shots"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMcb"/>
				<string id="name" value="Curls Ball"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMrk"/>
				<string id="name" value="Likes To Round Keeper"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMot"/>
				<string id="name" value="Likes To Try To Break Offside Trap"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMao"/>
				<string id="name" value="Argues With Officials"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMlk"/>
				<string id="name" value="Likes To Lob Keeper"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMnt"/>
				<string id="name" value="Plays No Through Balls"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMdb"/>
				<string id="name" value="Dwells On Ball"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMaa"/>
				<string id="name" value="Arrives Late In Opposition Area"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMpw"/>
				<string id="name" value="Tries To Play Way Out Of Trouble"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMsb"/>
				<string id="name" value="Stays Back At All Times"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMdt"/>
				<string id="name" value="Dives Into Tackles"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMnd"/>
				<string id="name" value="Does Not Dive Into Tackles"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMfp"/>
				<string id="name" value="Hits Freekicks With Power"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMro"/>
				<string id="name" value="Runs With Ball Often"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMbr"/>
				<string id="name" value="Runs With Ball Rarely"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMsd"/>
				<string id="name" value="Shoots From Distance"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMaw"/>
				<string id="name" value="Avoids Using Weaker Foot"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMlr"/>
				<string id="name" value="Tries Long Range Free Kicks"/>
				<flags id="type" value="boolean"/>
			</record>
			<record>
				<flags id="field" value="PMci"/>
				<string id="name" value="Cuts Inside"/>
				<flags id="type" value="boolean"/>
			</record>
      <record>
        <flags id="field" value="PMcd"/>
        <string id="name" value="Comes Deep To Get Ball"/>
        <flags id="type" value="boolean"/>
      </record>
      <record>
        <flags id="field" value="PMhl"/>
        <string id="name" value="Hugs Line"/>
        <flags id="type" value="boolean"/>
      </record>
      <record>
        <flags id="field" value="PMlp"/>
        <string id="name" value="Looks For Pass Rather Than Attempting To Score"/>
        <flags id="type" value="boolean"/>
      </record>
      <record>
        <flags id="field" value="PMmt"/>
        <string id="name" value="Marks Opponent Tightly"/>
        <flags id="type" value="boolean"/>
      </record>
      <record>
        <flags id="field" value="PMbg"/>
        <string id="name" value="Plays With Back To Goal"/>
        <flags id="type" value="boolean"/>
      </record>
      <record>
        <flags id="field" value="PMlf"/>
        <string id="name" value="Possesses Long Flat Throw[COMMENT: player preferred move]"/>
        <flags id="type" value="boolean"/>
      </record>
      <record>
        <flags id="field" value="PMst"/>
        <string id="name" value="Stops Play"/>
        <flags id="type" value="boolean"/>
      </record>
      <record>
        <flags id="field" value="PMft"/>
        <string id="name" value="Tries First Time Shots"/>
        <flags id="type" value="boolean"/>
      </record>


      <!-- position -->
			<record>
				<flags id="field" value="Ppos"/>
				<string id="name" value="Position"/>
				<flags id="type" value="integer"/>
			</record>

			<!-- position string -->
			<record>
				<flags id="field" value="Ppsr"/>
				<string id="name" value="Position"/>
				<flags id="type" value="string_and_sort_order"/>
			</record>
			
			<!-- is injured -->
			<record>
				<flags id="field" value="Phij"/>
				<string id="name" value="Is Injured"/>
				<flags id="type" value="boolean"/>
				<boolean id="is_read_only" value="true"/>
			</record>

			<!-- home grown in nation -->
			<record>
				<flags id="field" value="Phgn"/>
				<string id="name" value="Trained in nation[COMMENT: home grown status label]"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- nation -->
						<record>
							<flags id="field" value="nati"/>
							<string id="name" value="Nation"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="nation"/>
						</record>
					</list>
				</record>
			</record>
					
			<!-- home grown in club -->
			<record>
				<flags id="field" value="Phgc"/>
				<string id="name" value="Trained at club[COMMENT: home grown status label]"/>
				<flags id="type" value="list"/>
				<record id="list_item">
					<list id="fields">

						<!-- team -->
						<record>
							<flags id="field" value="team"/>
							<string id="name" value="Team"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="team"/>
						</record>
					</list>
				</record>
			</record>
			
			<!-- injuries -->
			<record>
				<flags id="field" value="Pijl"/>
				<string id="name" value="Injuries"/>
				<flags id="type" value="list"/>	
				<record id="list_item">
					<list id="fields">

						<!-- injury -->
						<record>
							<flags id="field" value="inju"/>
							<string id="name" value="Injury"/>
							<flags id="type" value="database_record_unique_id"/>
							<flags id="database_table_type" value="injury"/>
						</record>

						<!-- start date -->
						<record>
							<flags id="field" value="stdt"/>
							<string id="name" value="Start Date"/>
							<flags id="type" value="date"/>
						</record>

						<!-- end date -->
						<record>
							<flags id="field" value="endt"/>
							<string id="name" value="End Date"/>
							<flags id="type" value="date"/>
						</record>

						<!-- future -->
						<record>
							<flags id="field" value="futu"/>
							<string id="name" value="Future"/>
							<flags id="type" value="boolean"/>
						</record>

						<!-- permanent -->
						<record>
							<flags id="field" value="perm"/>
							<string id="name" value="Permanent"/>
							<flags id="type" value="boolean"/>
						</record>

						<!-- severity -->
						<record>
							<flags id="field" value="seve"/>
							<string id="name" value="Severity"/>
							<flags id="type" value="integer"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="30"/>
						</record>
					</list>
				</record>
			</record>
									  
			<!-- bans -->
			<record>
				<flags id="field" value="Pbnl"/>
				<string id="name" value="Bans"/>
				<flags id="type" value="list"/>	
				<record id="list_item">
					<list id="fields">

						<!-- Ban Type -->
						<record>
							<flags id="field" value="type"/>
							<string id="name" value="Ban Type"/>
							<flags id="type" value="enum"/>
							<list id="enum_values">
								<record enum="0" name="Global Ban[COMMENT: ban type]"/>
								<record enum="1" name="League Ban[COMMENT: ban type]"/>
								<record enum="2" name="Cup Ban[COMMENT: ban type]"/>
								<record enum="3" name="Domestic Ban[COMMENT: ban type]"/>
								<record enum="4" name="Continental Ban[COMMENT: ban type]"/>
								<record enum="5" name="International Ban[COMMENT: ban type]"/>
								<record enum="6" name="Unavailable[COMMENT: ban type]"/>
								<record enum="7" name="Sabbatical[COMMENT: ban type]"/>
								<record enum="8" name="Injury Reasons[COMMENT: ban type]"/>
								<record enum="9" name="Medical Reasons[COMMENT: ban type]"/>
								<record enum="10" name="Personal Reasons[COMMENT: ban type]"/>
								<record enum="11" name="On Holiday[COMMENT: ban type]"/>
								<record enum="12" name="Study Leave[COMMENT: ban type]"/>
								<record enum="13" name="Military Service[COMMENT: ban type]"/>
							</list>
						</record>

						<!-- end date -->
						<record>
							<flags id="field" value="endt"/>
							<string id="name" value="End Date"/>
							<flags id="type" value="date"/>
							<boolean id="is_optional" value="true"/>
						</record>

						<!-- number matches -->
						<record>
							<flags id="field" value="matc"/>
							<string id="name" value="Number Matches"/>
							<flags id="type" value="integer"/>
							<boolean id="is_optional" value="true"/>
							<integer id="min_value" value="0"/>
							<integer id="max_value" value="127"/>
						</record>
					</list>
				</record>
			</record>
					
			<!-- retirement date -->
			<record>
				<flags id="field" value="PRdt"/>
				<string id="name" value="Retirement Date"/>
				<flags id="type" value="date"/>
				<boolean id="is_optional" value="true"/>
			</record>
		</list>
	</record>
</properties>

import xml.etree.ElementTree as ET
import csv
import json
import tkinter as tk
from tkinter import filedialog, messagebox
import os
import io

def load_xml_mapping():
    """Carica il mapping XML da un file JSON"""
    try:
        with open('xml_mapping.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        messagebox.showerror("Errore", f"Impossibile caricare il file xml_mapping.json: {str(e)}")
        return None

def parse_xml_content(root):
    """Analizza il contenuto XML e restituisce i dati estratti"""
    # Estrae i dati dal file XML
    player_data = {}
    # Mantiene l'ordine delle proprietà come appaiono nel file XML
    property_order = []
    
    # Cerca gli elementi specifici richiesti (nazionalità e club)
    # Cerca il primo ID della nazionalità (Nnat)
    nnat_elem = root.find('.//record[@id="new_value"]/integer[@id="Nnat"]')
    if nnat_elem is not None:
        player_data["NAZIONALITA_ID"] = nnat_elem.get('value')
        property_order.append("NAZIONALITA_ID")
    
    # Cerca il primo ID del club (Ttea)
    ttea_elem = root.find('.//record[@id="new_value"]/integer[@id="Ttea"]')
    if ttea_elem is not None:
        player_data["CLUB_ID"] = ttea_elem.get('value')
        property_order.append("CLUB_ID")
    
    # Cerca il primo ID della città (Tcit)
    tcit_elem = root.find('.//record[@id="new_value"]/integer[@id="Tcit"]')
    if tcit_elem is not None:
        player_data["CITTA_ID"] = tcit_elem.get('value')
        property_order.append("CITTA_ID")
    
    # Cerca tutti i record nel file XML
    for record in root.findall('.//record'):
        # Verifica se il record contiene una proprietà
        property_elem = record.find('./unsigned[@id="property"]')
        if property_elem is not None:
            property_id = property_elem.get('value')
            
            # Cerca il valore della proprietà
            value_elem = record.find('.//*[@id="new_value"]')
            if value_elem is not None:
                # Gestisce diversi tipi di valori
                if value_elem.tag == 'string':
                    value = value_elem.get('value')
                elif value_elem.tag == 'integer':
                    value = value_elem.get('value')
                elif value_elem.tag == 'date':
                    # Formatta la data come DD/MM/YYYY
                    day = value_elem.get('day')
                    # Il mese è 0-based nel file XML, quindi aggiungiamo 1
                    month = int(value_elem.get('month')) + 1
                    year = value_elem.get('year')
                    value = f"{day}/{month}/{year}"
                elif value_elem.tag == 'record':
                    # Gestisce elementi record annidati (nazionalità, club, città)
                    # Cerca l'ID della nazionalità (Nnat)
                    nnat_elem = value_elem.find('./integer[@id="Nnat"]')
                    if nnat_elem is not None:
                        value = nnat_elem.get('value')
                        # Aggiungi anche l'ID della nazionalità come proprietà separata
                        player_data[f"{property_id}_NNAT_ID"] = value
                        property_order.append(f"{property_id}_NNAT_ID")
                    
                    # Cerca l'ID del club (Ttea)
                    ttea_elem = value_elem.find('./integer[@id="Ttea"]')
                    if ttea_elem is not None:
                        value = ttea_elem.get('value')
                        # Aggiungi anche l'ID del club come proprietà separata
                        player_data[f"{property_id}_TTEA_ID"] = value
                        property_order.append(f"{property_id}_TTEA_ID")
                    
                    # Cerca l'ID della città (Tcit)
                    tcit_elem = value_elem.find('./integer[@id="Tcit"]')
                    if tcit_elem is not None:
                        value = tcit_elem.get('value')
                        # Aggiungi anche l'ID della città come proprietà separata
                        player_data[f"{property_id}_TCIT_ID"] = value
                        property_order.append(f"{property_id}_TCIT_ID")
                    
                    # Se non abbiamo trovato nessun ID specifico, usa il valore come stringa
                    if 'value' not in locals() or value is None:
                        value = "record_annidato"
                else:
                    # Per altri tipi di valori, usa il valore come stringa
                    value = str(value_elem.get('value', ''))
                
                # Aggiunge la proprietà e il valore ai dati del giocatore
                player_data[property_id] = value
                property_order.append(property_id)
    
    # Restituisce sia i dati che l'ordine delle proprietà
    return player_data, property_order
    
    return player_data

def parse_xml_file(xml_file_path):
    """Analizza un file XML e restituisce i dati estratti e l'ordine delle proprietà"""
    try:
        # Legge il file XML con codifica UTF-16
        try:
            # Prima prova con codifica UTF-16
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            return parse_xml_content(root)
        except Exception as e:
            # Se fallisce, prova a leggere il file manualmente con codifica UTF-16
            try:
                with open(xml_file_path, 'r', encoding='utf-16') as f:
                    xml_content = f.read()
                root = ET.fromstring(xml_content)
                return parse_xml_content(root)
            except Exception as e2:
                # Prova con altre codifiche
                with open(xml_file_path, 'r', encoding='utf-8') as f:
                    xml_content = f.read()
                root = ET.fromstring(xml_content)
                return parse_xml_content(root)
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nell'analisi del file XML: {str(e)}")
        return None

def convert_xml_to_csv(xml_file_path, csv_file_path, xml_mapping):
    """Converte un file XML in un file CSV"""
    try:
        # Analizza il file XML
        result = parse_xml_file(xml_file_path)
        if result is None:
            return False
        
        # Estrai i dati e l'ordine delle proprietà
        player_data, property_order = result
        
        # Prepara i dati per il CSV
        csv_data = {}
        
        # Crea un elenco di tutte le colonne nell'ordine originale
        fieldnames = []
        
        # Processa le proprietà nell'ordine in cui appaiono nel file XML
        for property_id in property_order:
            # Gestisci le colonne speciali
            if property_id in ["NAZIONALITA_ID", "CLUB_ID", "CITTA_ID"]:
                csv_data[property_id] = player_data[property_id]
                fieldnames.append(property_id)
            else:
                # Cerca il nome della proprietà nel mapping
                if property_id in xml_mapping:
                    property_name = xml_mapping[property_id]
                    csv_data[property_name] = player_data[property_id]
                    fieldnames.append(property_name)
                else:
                    # Se la proprietà non è nel mapping, usa l'ID come nome
                    column_name = f"PROP_{property_id}"
                    csv_data[column_name] = player_data[property_id]
                    fieldnames.append(column_name)
        
        # Scrive i dati nel file CSV
        with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerow(csv_data)
        
        return True
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nella conversione XML a CSV: {str(e)}")
        return False

def main():
    # Crea l'interfaccia grafica
    root = tk.Tk()
    root.title("Convertitore XML a CSV per Football Manager")
    root.geometry("500x200")
    
    # Carica il mapping XML
    xml_mapping = load_xml_mapping()
    if xml_mapping is None:
        root.destroy()
        return
    
    # Funzione per selezionare il file XML e convertirlo in CSV
    def select_and_convert():
        # Chiede all'utente di selezionare il file XML
        xml_file_path = filedialog.askopenfilename(
            title="Seleziona il file XML",
            filetypes=[("XML files", "*.xml")]
        )
        
        if not xml_file_path:
            return
        
        # Chiede all'utente dove salvare il file CSV
        csv_file_path = filedialog.asksaveasfilename(
            title="Salva il file CSV",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")],
            initialfile=os.path.splitext(os.path.basename(xml_file_path))[0] + ".csv"
        )
        
        if not csv_file_path:
            return
        
        # Converte il file XML in CSV
        if convert_xml_to_csv(xml_file_path, csv_file_path, xml_mapping):
            messagebox.showinfo("Successo", f"File CSV generato con successo: {csv_file_path}")
    
    # Crea i widget dell'interfaccia
    tk.Label(root, text="Convertitore XML a CSV per Football Manager", font=("Helvetica", 14)).pack(pady=20)
    tk.Label(root, text="Questo strumento converte un file XML di Football Manager in un file CSV.").pack()
    tk.Button(root, text="Seleziona file XML e converti in CSV", command=select_and_convert).pack(pady=20)
    
    # Avvia l'interfaccia grafica
    root.mainloop()

if __name__ == "__main__":
    main()
import xml.etree.ElementTree as ET
import csv
import json
import tkinter as tk
from tkinter import filedialog, messagebox
import os
import shutil
from datetime import datetime

def load_xml_mapping():
    """Carica il mapping XML da un file JSON"""
    try:
        with open('xml_mapping.json', 'r', encoding='utf-8') as f:
            mapping = json.load(f)
            # Crea anche il mapping inverso (nome colonna -> ID proprietà)
            reverse_mapping = {v: k for k, v in mapping.items()}
            return mapping, reverse_mapping
    except Exception as e:
        messagebox.showerror("Errore", f"Impossibile caricare il file xml_mapping.json: {str(e)}")
        return None, None

def parse_csv_file(csv_file_path):
    """Legge il file CSV e restituisce i dati"""
    try:
        csv_data = {}
        with open(csv_file_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            # Assumiamo che ci sia solo una riga di dati (un giocatore)
            for row in reader:
                csv_data = row
                break
        return csv_data
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nella lettura del file CSV: {str(e)}")
        return None

def update_xml_element(element, new_value):
    """Aggiorna un elemento XML con un nuovo valore"""
    if element.tag == 'string':
        element.set('value', str(new_value))
    elif element.tag == 'integer':
        try:
            # Verifica che sia un numero valido
            int(new_value)
            element.set('value', str(new_value))
        except ValueError:
            print(f"Avviso: Valore non numerico '{new_value}' per elemento integer, ignorato")
    elif element.tag == 'date':
        # Gestisce le date nel formato DD/MM/YYYY
        try:
            if '/' in str(new_value):
                day, month, year = str(new_value).split('/')
                element.set('day', day)
                element.set('month', str(int(month) - 1))  # Il mese è 0-based nel XML
                element.set('year', year)
            else:
                print(f"Avviso: Formato data non valido '{new_value}', ignorato")
        except ValueError:
            print(f"Avviso: Formato data non valido '{new_value}', ignorato")

def update_xml_with_csv_data(xml_file_path, csv_data, xml_mapping, reverse_mapping):
    """Aggiorna il file XML con i dati del CSV"""
    try:
        # Crea un backup del file originale
        backup_path = xml_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(xml_file_path, backup_path)
        print(f"Backup creato: {backup_path}")
        
        # Carica il file XML
        try:
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
        except Exception:
            # Prova con codifica UTF-16
            with open(xml_file_path, 'r', encoding='utf-16') as f:
                xml_content = f.read()
            root = ET.fromstring(xml_content)
            tree = ET.ElementTree(root)
        
        # Processa ogni colonna del CSV
        for column_name, new_value in csv_data.items():
            if not new_value or new_value.strip() == '':
                continue  # Salta valori vuoti
            
            # Gestisce colonne speciali per ID di nazionalità, club, città
            if column_name.endswith('_NNAT_ID'):
                # Trova l'elemento corrispondente nel XML
                property_id = column_name.replace('_NNAT_ID', '')
                xpath = f'.//record/unsigned[@id="property"][@value="{property_id}"]/..//integer[@id="Nnat"]'
                elements = root.findall(xpath)
                for elem in elements:
                    elem.set('value', str(new_value))
                    print(f"Aggiornato {column_name}: {new_value}")
                continue
            
            elif column_name.endswith('_TTEA_ID'):
                # Trova l'elemento corrispondente nel XML
                property_id = column_name.replace('_TTEA_ID', '')
                xpath = f'.//record/unsigned[@id="property"][@value="{property_id}"]/..//integer[@id="Ttea"]'
                elements = root.findall(xpath)
                for elem in elements:
                    elem.set('value', str(new_value))
                    print(f"Aggiornato {column_name}: {new_value}")
                continue
            
            elif column_name.endswith('_TCIT_ID'):
                # Trova l'elemento corrispondente nel XML
                property_id = column_name.replace('_TCIT_ID', '')
                xpath = f'.//record/unsigned[@id="property"][@value="{property_id}"]/..//integer[@id="Tcit"]'
                elements = root.findall(xpath)
                for elem in elements:
                    elem.set('value', str(new_value))
                    print(f"Aggiornato {column_name}: {new_value}")
                continue
            
            # Gestisce colonne che iniziano con PROP_
            elif column_name.startswith('PROP_'):
                property_id = column_name.replace('PROP_', '')
            else:
                # Cerca l'ID della proprietà nel mapping inverso
                if column_name in reverse_mapping:
                    property_id = reverse_mapping[column_name]
                else:
                    print(f"Avviso: Colonna '{column_name}' non trovata nel mapping, ignorata")
                    continue
            
            # Trova e aggiorna l'elemento nel XML
            xpath = f'.//record/unsigned[@id="property"][@value="{property_id}"]/..//*[@id="new_value"]'
            elements = root.findall(xpath)
            
            if elements:
                for elem in elements:
                    update_xml_element(elem, new_value)
                    print(f"Aggiornato {column_name} (ID: {property_id}): {new_value}")
            else:
                print(f"Avviso: Elemento per '{column_name}' (ID: {property_id}) non trovato nel XML")
        
        # Salva il file XML modificato
        # Determina la codifica originale
        try:
            tree.write(xml_file_path, encoding='utf-16', xml_declaration=True)
        except Exception:
            tree.write(xml_file_path, encoding='utf-8', xml_declaration=True)
        
        return True
        
    except Exception as e:
        messagebox.showerror("Errore", f"Errore nell'aggiornamento del file XML: {str(e)}")
        return False

def main():
    # Crea l'interfaccia grafica
    root = tk.Tk()
    root.title("Convertitore CSV a XML per Football Manager")
    root.geometry("600x300")
    
    # Carica il mapping XML
    xml_mapping, reverse_mapping = load_xml_mapping()
    if xml_mapping is None or reverse_mapping is None:
        root.destroy()
        return
    
    def select_and_convert():
        # Chiede all'utente di selezionare il file CSV
        csv_file_path = filedialog.askopenfilename(
            title="Seleziona il file CSV modificato",
            filetypes=[("CSV files", "*.csv")]
        )
        
        if not csv_file_path:
            return
        
        # Chiede all'utente di selezionare il file XML da aggiornare
        xml_file_path = filedialog.askopenfilename(
            title="Seleziona il file XML originale da aggiornare",
            filetypes=[("XML files", "*.xml")]
        )
        
        if not xml_file_path:
            return
        
        # Legge i dati dal CSV
        csv_data = parse_csv_file(csv_file_path)
        if csv_data is None:
            return
        
        # Conferma dall'utente
        result = messagebox.askyesno(
            "Conferma", 
            f"Sei sicuro di voler aggiornare il file XML?\n\n"
            f"File XML: {xml_file_path}\n"
            f"File CSV: {csv_file_path}\n\n"
            f"Verrà creato un backup automatico del file originale."
        )
        
        if not result:
            return
        
        # Aggiorna il file XML
        if update_xml_with_csv_data(xml_file_path, csv_data, xml_mapping, reverse_mapping):
            messagebox.showinfo("Successo", f"File XML aggiornato con successo!\n\nUn backup è stato creato automaticamente.")
    
    # Crea i widget dell'interfaccia
    tk.Label(root, text="Convertitore CSV a XML per Football Manager", font=("Helvetica", 14)).pack(pady=20)
    tk.Label(root, text="Questo strumento aggiorna un file XML con le modifiche apportate al CSV.").pack()
    tk.Label(root, text="ATTENZIONE: Verrà creato un backup automatico del file XML originale.", fg="red").pack(pady=5)
    tk.Button(root, text="Seleziona CSV e XML per aggiornare", command=select_and_convert).pack(pady=20)
    
    # Avvia l'interfaccia grafica
    root.mainloop()

if __name__ == "__main__":
    main()
